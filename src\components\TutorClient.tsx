'use client';

import { useState, useRef, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import MarkdownRenderer from '@/components/MarkdownRenderer';
import AdSense from '@/components/AdSense';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  explanationType?: string;
}

interface AIResponse {
  answer: string;
  explanation: string;
  examples: string[];
  tips: string[];
  relatedTopics: string[];
  commonMistakes: string[];
  practiceExercises: string[];
}

export default function TutorClient() {
  const { language, t } = useLanguage();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedType, setSelectedType] = useState('general');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const explanationTypes = [
    { id: 'grammar', name: 'Grammar', icon: '📝', description: 'Grammar rules and structures' },
    { id: 'vocabulary', name: 'Vocabulary', icon: '📚', description: 'Word meanings and usage' },
    { id: 'pronunciation', name: 'Pronunciation', icon: '🗣️', description: 'Pronunciation and phonetics' },
    { id: 'usage', name: 'Usage', icon: '💬', description: 'Proper usage in context' },
    { id: 'cultural', name: 'Cultural', icon: '🌍', description: 'Cultural context and appropriateness' },
    { id: 'general', name: 'General', icon: '❓', description: 'General English questions' }
  ];

  const quickQuestions = [
    "What's the difference between 'a' and 'an'?",
    "How do I use present perfect tense?",
    "When should I use 'who' vs 'whom'?",
    "What are some common phrasal verbs?",
    "How do I improve my pronunciation?",
    "What's the difference between 'affect' and 'effect'?"
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Add welcome message when component mounts
    if (messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        type: 'ai',
        content: `Hello! I'm your AI English tutor. I'm here to help you with grammar, vocabulary, pronunciation, and any other English learning questions you might have. 

What would you like to learn about today?`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (messageText?: string) => {
    const text = messageText || inputMessage.trim();
    if (!text || isLoading) return;

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: text,
      timestamp: new Date(),
      explanationType: selectedType
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/ai/explanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: text,
          language,
          context: '',
          explanationType: selectedType
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiResponse: AIResponse = data.explanation;
        
        // Format the AI response
        let formattedResponse = `## ${aiResponse.answer}\n\n`;
        formattedResponse += `${aiResponse.explanation}\n\n`;
        
        if (aiResponse.examples && aiResponse.examples.length > 0) {
          formattedResponse += `### Examples:\n`;
          aiResponse.examples.forEach((example, index) => {
            formattedResponse += `${index + 1}. ${example}\n`;
          });
          formattedResponse += '\n';
        }

        if (aiResponse.tips && aiResponse.tips.length > 0) {
          formattedResponse += `### Tips:\n`;
          aiResponse.tips.forEach((tip) => {
            formattedResponse += `💡 ${tip}\n`;
          });
          formattedResponse += '\n';
        }

        if (aiResponse.commonMistakes && aiResponse.commonMistakes.length > 0) {
          formattedResponse += `### Common Mistakes to Avoid:\n`;
          aiResponse.commonMistakes.forEach((mistake) => {
            formattedResponse += `⚠️ ${mistake}\n`;
          });
          formattedResponse += '\n';
        }

        if (aiResponse.practiceExercises && aiResponse.practiceExercises.length > 0) {
          formattedResponse += `### Practice Suggestions:\n`;
          aiResponse.practiceExercises.forEach((exercise) => {
            formattedResponse += `📝 ${exercise}\n`;
          });
          formattedResponse += '\n';
        }

        if (aiResponse.relatedTopics && aiResponse.relatedTopics.length > 0) {
          formattedResponse += `### Related Topics:\n`;
          aiResponse.relatedTopics.forEach((topic) => {
            formattedResponse += `🔗 ${topic}\n`;
          });
        }

        const aiMessage: ChatMessage = {
          id: `ai_${Date.now()}`,
          type: 'ai',
          content: formattedResponse,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, aiMessage]);
      } else {
        throw new Error(data.error || 'Failed to get response');
      }
    } catch (error) {
      console.error('Error getting AI response:', error);
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'ai',
        content: t('aiErrorMessage'),
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header
        onAuthClick={() => setShowAuthModal(true)}
        showBackButton={true}
        onBackClick={() => window.history.back()}
      />

      <div className="pt-24 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4">
              {t('tutorTitle')}
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
              {t('tutorSubtitle')}
            </p>
          </div>

          {/* Question Type Selector */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">What type of help do you need?</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
              {explanationTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setSelectedType(type.id)}
                  className={`p-3 rounded-xl text-center transition-all duration-300 ${
                    selectedType === type.id
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg scale-105'
                      : 'bg-white/80 hover:bg-white text-gray-700 hover:shadow-md'
                  }`}
                  title={type.description}
                >
                  <div className="text-xl mb-1">{type.icon}</div>
                  <div className="text-xs font-medium">{type.name}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Quick Questions */}
          {messages.length <= 1 && (
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Questions to Get Started:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {quickQuestions.map((question, index) => (
                  <button
                    key={index}
                    onClick={() => handleSendMessage(question)}
                    className="p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm text-gray-700"
                    disabled={isLoading}
                  >
                    {question}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Chat Messages */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 mb-6">
            <div className="h-96 overflow-y-auto p-6 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] p-4 rounded-2xl ${
                      message.type === 'user'
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {message.type === 'ai' ? (
                      <div className="prose prose-sm max-w-none">
                        <MarkdownRenderer content={message.content} />
                      </div>
                    ) : (
                      <p className="text-sm">{message.content}</p>
                    )}
                    <div className={`text-xs mt-2 ${
                      message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 text-gray-800 p-4 rounded-2xl">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      <span className="text-sm">Thinking...</span>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex space-x-3">
                <textarea
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder={t('askQuestion')}
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={2}
                  disabled={isLoading}
                />
                <button
                  onClick={() => handleSendMessage()}
                  disabled={!inputMessage.trim() || isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {t('send')}
                </button>
              </div>
            </div>
          </div>

          {/* AdSense Banner */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8">
            <AdSense
              adSlot="4567890123"
              adFormat="auto"
              className="rounded-lg"
              style={{ display: 'block', minHeight: '100px' }}
              lazy={true}
            />
          </div>

          {/* Educational Content */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8">
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">What You Can Ask Me</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-blue-800 mb-2">📝 Grammar Questions</h4>
                  <ul className="text-gray-700 space-y-1">
                    <li>• Verb tenses and conjugations</li>
                    <li>• Sentence structure and syntax</li>
                    <li>• Parts of speech usage</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-green-800 mb-2">📚 Vocabulary Help</h4>
                  <ul className="text-gray-700 space-y-1">
                    <li>• Word meanings and definitions</li>
                    <li>• Synonyms and antonyms</li>
                    <li>• Usage in different contexts</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-purple-800 mb-2">🗣️ Pronunciation Guide</h4>
                  <ul className="text-gray-700 space-y-1">
                    <li>• Phonetic transcriptions</li>
                    <li>• Stress patterns</li>
                    <li>• Common pronunciation mistakes</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-orange-800 mb-2">💬 Usage & Context</h4>
                  <ul className="text-gray-700 space-y-1">
                    <li>• Formal vs informal language</li>
                    <li>• Cultural appropriateness</li>
                    <li>• Common expressions and idioms</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />

      <Footer />
    </div>
  );
}
