'use client';

import { useEffect, useRef, useState } from 'react';

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

interface AdSenseProps {
  adSlot: string;
  adFormat?: string;
  fullWidthResponsive?: boolean;
  style?: React.CSSProperties;
  className?: string;
  lazy?: boolean;
}

export default function AdSense({
  adSlot,
  adFormat = 'auto',
  fullWidthResponsive = true,
  style = { display: 'block' },
  className = '',
  lazy = true
}: AdSenseProps) {
  const adRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(!lazy);
  const [isLoaded, setIsLoaded] = useState(false);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isVisible) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '100px', // Load ads 100px before they come into view
        threshold: 0.1
      }
    );

    if (adRef.current) {
      observer.observe(adRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isVisible]);

  useEffect(() => {
    if (!isVisible || isLoaded) return;

    // Load AdSense script if not already loaded
    if (!document.querySelector('script[src*="adsbygoogle.js"]')) {
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID}`;
      script.crossOrigin = 'anonymous';
      script.onload = () => setIsLoaded(true);
      document.head.appendChild(script);
    } else {
      setIsLoaded(true);
    }

    // Initialize ads after script loads
    const initAds = () => {
      try {
        if (window.adsbygoogle && isLoaded) {
          window.adsbygoogle.push({});
        }
      } catch (error) {
        console.error('AdSense error:', error);
      }
    };

    // Small delay to ensure script is loaded
    const timer = setTimeout(initAds, 100);
    return () => clearTimeout(timer);
  }, [isVisible, isLoaded]);

  // Don't render ads in development
  if (process.env.NODE_ENV === 'development') {
    return (
      <div
        ref={adRef}
        className={`bg-gray-100 border-2 border-dashed border-gray-300 p-4 text-center text-gray-500 ${className}`}
        style={{ minHeight: '100px', ...style }}
      >
        <div className="text-sm">AdSense Ad Placeholder</div>
        <div className="text-xs">Slot: {adSlot}</div>
        <div className="text-xs">Lazy: {lazy ? 'Yes' : 'No'}</div>
      </div>
    );
  }

  return (
    <div ref={adRef} className={className} style={{ minHeight: '100px' }}>
      {isVisible && (
        <ins
          className="adsbygoogle"
          style={style}
          data-ad-client={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID}
          data-ad-slot={adSlot}
          data-ad-format={adFormat}
          data-full-width-responsive={fullWidthResponsive}
          data-ad-test={'off'}
        />
      )}
    </div>
  );
}
