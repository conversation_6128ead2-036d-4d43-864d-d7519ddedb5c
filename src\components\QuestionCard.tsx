'use client';

import { Question } from '@/types';
import { useLanguage } from '@/contexts/LanguageContext';

interface QuestionCardProps {
  question: Question;
  selectedAnswer: number | null;
  onAnswerSelect: (answerIndex: number) => void;
  showResult: boolean;
}

export default function QuestionCard({
  question,
  selectedAnswer,
  onAnswerSelect,
  showResult
}: QuestionCardProps) {
  const { t } = useLanguage();

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-200';
      case 'intermediate': return 'bg-gradient-to-r from-amber-100 to-orange-100 text-orange-800 border-orange-200';
      case 'advanced': return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default: return 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'beginner': return '🌱';
      case 'intermediate': return '🌿';
      case 'advanced': return '🌳';
      default: return '📚';
    }
  };

  return (
    <div className="flex items-center justify-center p-3 sm:p-4 quiz-container">
      <div className="max-w-4xl w-full">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-50/80 via-indigo-50/80 to-purple-50/80 backdrop-blur-sm px-4 sm:px-6 py-3 border-b border-white/20">
            <div className="flex items-center justify-between mb-2">
              <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-xl text-xs font-bold border shadow-sm ${getLevelColor(question.level)}`}>
                <span className="text-sm">{getLevelIcon(question.level)}</span>
                <span>{t(question.level)}</span>
              </div>
              <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm rounded-lg px-2 py-1.5 border border-white/30">
                <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-xs font-semibold text-gray-700">{t('question')}</span>
              </div>
            </div>

            <h2 className="text-lg sm:text-xl font-bold text-gray-800 leading-tight">
              {question.question}
            </h2>
          </div>

          {/* Options */}
          <div className="p-4 sm:p-6">
            <div className="space-y-3">
              {question.options.map((option, index) => {
                let buttonClass = "group w-full p-3 sm:p-4 text-left rounded-xl border-2 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300/50 transform hover:-translate-y-0.5 ";

                if (showResult) {
                  if (index === question.correctAnswer) {
                    buttonClass += "bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-400 text-emerald-800 shadow-lg shadow-emerald-200/50";
                  } else if (index === selectedAnswer && index !== question.correctAnswer) {
                    buttonClass += "bg-gradient-to-r from-red-50 to-pink-50 border-red-400 text-red-800 shadow-lg shadow-red-200/50";
                  } else {
                    buttonClass += "bg-gray-50/50 border-gray-200 text-gray-600 opacity-60";
                  }
                } else {
                  if (selectedAnswer === index) {
                    buttonClass += "bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-400 text-blue-800 shadow-lg shadow-blue-200/50 scale-105";
                  } else {
                    buttonClass += "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 hover:border-blue-300 hover:shadow-lg hover:shadow-blue-200/30";
                  }
                }

                return (
                  <button
                    key={index}
                    onClick={() => !showResult && onAnswerSelect(index)}
                    disabled={showResult}
                    className={buttonClass}

                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-white to-gray-100 border-2 border-current rounded-lg flex items-center justify-center font-bold text-sm shadow-sm group-hover:scale-110 transition-transform duration-200">
                        {String.fromCharCode(65 + index)}
                      </div>
                      <span className="flex-1 text-sm sm:text-base leading-relaxed font-medium">
                        {option}
                      </span>
                      {selectedAnswer === index && !showResult && (
                        <div className="flex-shrink-0">
                          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg className="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>

            {/* Instructions */}
            <div className="mt-4 p-3 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-sm rounded-xl border border-blue-200/50 shadow-sm">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="text-xs font-bold text-blue-800 mb-1">
                    {selectedAnswer !== null ? `✨ ${t('processingAnswer')}` : `👆 ${t('chooseAnswer')}`}
                  </div>
                  <div className="text-xs text-blue-600">
                    {selectedAnswer !== null ? t('answerSelected') || 'Answer selected! Checking...' : t('selectAnswer') || 'Select the best answer from the options above'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
